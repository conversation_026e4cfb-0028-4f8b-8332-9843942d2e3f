#!/usr/bin/env python3
"""
Simple Production Notification Test

A simplified version to test the production notification functions.
"""

import asyncio
import json
import os
import sys
from pathlib import Path
from unittest.mock import patch

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

# Load environment variables
from dotenv import load_dotenv
load_dotenv(backend_dir / '.env')

# Initialize Firebase
import firebase_admin

try:
    firebase_admin.get_app()
    print("✓ Firebase already initialized")
except ValueError:
    service_account_info = json.loads(os.environ["SERVICE_ACCOUNT_JSON"])
    credentials = firebase_admin.credentials.Certificate(service_account_info)
    firebase_admin.initialize_app(credentials)
    print("✓ Firebase initialized")

# Import functions
from utils.other.notifications import send_daily_notification, send_daily_summary_notification
from database.notifications import get_token

TARGET_USER_ID = "mcaK5709t3MZAcUpdAeEGrmYgaT2"

async def test_morning_notification():
    """Test morning notification with timezone bypass."""
    print("\n🌅 Testing Morning Notification...")
    
    # Get user token
    result = get_token(TARGET_USER_ID)
    if not result:
        print("❌ User not found")
        return False
    
    user_token, user_timezone = result
    print(f"✓ Found user: {user_token[:20]}..., timezone: {user_timezone}")
    
    # Bypass timezone by mocking the timezone function
    with patch('utils.other.notifications._get_timezones_at_time') as mock_tz:
        mock_tz.return_value = [user_timezone]
        
        with patch('database.notifications.get_users_token_in_timezones') as mock_users:
            mock_users.return_value = [user_token]
            
            print("📤 Sending morning notification...")
            result = await send_daily_notification()
            
            if result:
                print(f"✅ Morning notification sent! Users: {len(result)}")
                return True
            else:
                print("❌ Morning notification failed")
                return False

async def test_summary_notification():
    """Test summary notification with timezone bypass."""
    print("\n🌙 Testing Summary Notification...")
    
    # Get user token
    result = get_token(TARGET_USER_ID)
    if not result:
        print("❌ User not found")
        return False
    
    user_token, user_timezone = result
    print(f"✓ Found user: {user_token[:20]}..., timezone: {user_timezone}")
    
    # Bypass timezone by mocking the timezone function
    with patch('utils.other.notifications._get_timezones_at_time') as mock_tz:
        mock_tz.return_value = [user_timezone]
        
        with patch('database.notifications.get_users_id_in_timezones') as mock_users:
            mock_users.return_value = [(TARGET_USER_ID, user_token)]
            
            print("📤 Sending summary notification...")
            try:
                result = await send_daily_summary_notification()
                print("✅ Summary notification function completed")
                return True
            except Exception as e:
                print(f"❌ Summary notification failed: {e}")
                return False

async def main():
    print("🧪 Simple Production Notification Test")
    print("=" * 50)
    print(f"Target User: {TARGET_USER_ID}")
    print("⚠️  This will send REAL notifications!")
    
    response = input("\nContinue? (yes/no): ")
    if response.lower() != 'yes':
        print("Test cancelled.")
        return
    
    # Test morning notification
    morning_success = await test_morning_notification()
    
    # Wait between tests
    if morning_success:
        print("\n⏳ Waiting 5 seconds...")
        await asyncio.sleep(5)
    
    # Test summary notification
    summary_success = await test_summary_notification()
    
    # Summary
    print("\n" + "=" * 50)
    print("TEST RESULTS:")
    print(f"Morning Notification: {'✅ PASS' if morning_success else '❌ FAIL'}")
    print(f"Summary Notification: {'✅ PASS' if summary_success else '❌ FAIL'}")
    
    if morning_success and summary_success:
        print("\n🎉 All tests passed! Check your iOS app for notifications.")
    else:
        print("\n⚠️  Some tests failed.")

if __name__ == "__main__":
    asyncio.run(main())
