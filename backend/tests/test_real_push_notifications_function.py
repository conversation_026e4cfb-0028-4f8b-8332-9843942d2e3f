#!/usr/bin/env python3
"""
Real Push Notification Test - No Mocks, Real Firebase

This test script sends ACTUAL push notifications to your iOS app using real Firebase services.
It targets the specific user ID: mcaK5709t3MZAcUpdAeEGrmYgaT2

WARNING: This will send real notifications to your device!

Usage:
    python3 backend/tests/test_real_push_notifications.py [--dry-run]
"""

import asyncio
import json
import os
import sys
import argparse
import logging
from datetime import datetime, timezone, time
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    env_path = backend_dir / '.env'
    if env_path.exists():
        load_dotenv(env_path)
        print(f"✓ Loaded environment variables from {env_path}")
    else:
        print(f"⚠️  .env file not found at {env_path}")
except ImportError:
    print("ℹ python-dotenv not available, using system environment variables")

# Set up environment variables (no test overrides)
os.environ.setdefault('TESTING', 'false')  # Use real services

# Initialize Firebase first
import firebase_admin
import json

def initialize_firebase():
    """Initialize Firebase Admin SDK."""
    try:
        # Check if Firebase is already initialized
        firebase_admin.get_app()
        print("✓ Firebase already initialized")
        return True
    except ValueError:
        pass

    try:
        if os.environ.get('SERVICE_ACCOUNT_JSON'):
            service_account_info = json.loads(os.environ["SERVICE_ACCOUNT_JSON"])
            credentials = firebase_admin.credentials.Certificate(service_account_info)
            firebase_admin.initialize_app(credentials)
            print("✓ Firebase initialized with SERVICE_ACCOUNT_JSON")
            return True
        else:
            firebase_admin.initialize_app()
            print("✓ Firebase initialized with default credentials")
            return True
    except Exception as e:
        print(f"❌ Failed to initialize Firebase: {e}")
        return False

# Initialize Firebase before importing notification components
if not initialize_firebase():
    print("❌ Cannot proceed without Firebase initialization")
    sys.exit(1)

# Import notification components (after Firebase initialization)
from utils.notifications import send_notification, send_bulk_notification
from database.notifications import (
    get_token_only,
    get_token,
    get_users_token_in_timezones,
    get_users_id_in_timezones
)
from models.notification_message import NotificationMessage
from utils.other.notifications import (
    send_daily_notification,
    _get_timezones_at_time,
    _send_notification_for_time
)

# Color codes for output
class Colors:
    GREEN = '\033[92m'
    RED = '\033[91m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    BOLD = '\033[1m'
    END = '\033[0m'


class RealNotificationTester:
    """Test push notifications with real Firebase services."""
    
    TARGET_USER_ID = "mcaK5709t3MZAcUpdAeEGrmYgaT2"
    
    def __init__(self, dry_run=False):
        self.dry_run = dry_run
        self.target_user_id = self.TARGET_USER_ID
        self.logger = self.setup_logging()
        
    def setup_logging(self):
        """Setup logging configuration."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler('/tmp/real_notification_test.log', mode='a')
            ]
        )
        return logging.getLogger('RealNotificationTester')
    
    def print_header(self):
        """Print test header."""
        print(f"{Colors.BLUE}{Colors.BOLD}")
        print("=" * 70)
        print("  REAL PUSH NOTIFICATION TEST - NO MOCKS")
        print("=" * 70)
        print(f"{Colors.END}")
        print(f"{Colors.YELLOW}Target User ID: {self.target_user_id}{Colors.END}")
        print(f"{Colors.YELLOW}Dry Run Mode: {'ON' if self.dry_run else 'OFF'}{Colors.END}")
        if not self.dry_run:
            print(f"{Colors.RED}⚠️  REAL NOTIFICATIONS WILL BE SENT TO YOUR iOS APP!{Colors.END}")
        print()
    
    def get_user_token_and_timezone(self):
        """Get the real FCM token and timezone for the target user."""
        try:
            self.logger.info(f"Retrieving token for user: {self.target_user_id}")
            result = get_token(self.target_user_id)
            
            if result:
                token, timezone = result
                self.logger.info(f"Found user token: {token[:20]}...")
                self.logger.info(f"User timezone: {timezone}")
                return token, timezone
            else:
                self.logger.error(f"No token found for user: {self.target_user_id}")
                return None, None
                
        except Exception as e:
            self.logger.error(f"Error retrieving user token: {e}")
            return None, None
    
    def test_simple_notification(self, token):
        """Send a simple test notification."""
        title = "🧪 Test Notification"
        body = f"Real push notification test at {datetime.now().strftime('%H:%M:%S')}"
        
        if self.dry_run:
            print(f"{Colors.YELLOW}DRY RUN: Would send notification:{Colors.END}")
            print(f"  Title: {title}")
            print(f"  Body: {body}")
            print(f"  Token: {token[:20]}...")
            return True
        
        self.logger.info("Sending simple test notification...")
        result = send_notification(token, title, body)
        
        if result:
            print(f"{Colors.GREEN}✓ Simple notification sent successfully!{Colors.END}")
            print(f"  Check your iOS app for: '{title}'")
        else:
            print(f"{Colors.RED}✗ Failed to send simple notification{Colors.END}")
        
        return result
    
    def test_notification_with_data(self, token):
        """Send a notification with custom data payload."""
        title = "📱 Data Notification Test"
        body = "This notification includes custom data payload"
        data = {
            "test_type": "real_notification_test",
            "timestamp": datetime.now().isoformat(),
            "user_id": self.target_user_id,
            "navigate_to": "/test"
        }
        
        if self.dry_run:
            print(f"{Colors.YELLOW}DRY RUN: Would send data notification:{Colors.END}")
            print(f"  Title: {title}")
            print(f"  Body: {body}")
            print(f"  Data: {data}")
            return True
        
        self.logger.info("Sending notification with data payload...")
        result = send_notification(token, title, body, data)
        
        if result:
            print(f"{Colors.GREEN}✓ Data notification sent successfully!{Colors.END}")
            print(f"  Check your iOS app for: '{title}'")
            print(f"  Data payload included: {data}")
        else:
            print(f"{Colors.RED}✗ Failed to send data notification{Colors.END}")
        
        return result
    
    def test_notification_message_format(self, token):
        """Send a notification using NotificationMessage format."""
        title = "🔔 Formatted Notification"
        body = "Testing NotificationMessage format for real delivery"
        
        # Create notification message
        notification_msg = NotificationMessage(
            text=body,
            from_integration='false',
            type='test',
            notification_type='real_test',
            navigate_to="/chat/omi"
        )
        
        data = NotificationMessage.get_message_as_dict(notification_msg)
        
        if self.dry_run:
            print(f"{Colors.YELLOW}DRY RUN: Would send formatted notification:{Colors.END}")
            print(f"  Title: {title}")
            print(f"  Message: {notification_msg.text}")
            print(f"  Type: {notification_msg.notification_type}")
            return True
        
        self.logger.info("Sending formatted notification message...")
        result = send_notification(token, title, body, data)
        
        if result:
            print(f"{Colors.GREEN}✓ Formatted notification sent successfully!{Colors.END}")
            print(f"  Check your iOS app for: '{title}'")
            print(f"  Message ID: {notification_msg.id}")
        else:
            print(f"{Colors.RED}✗ Failed to send formatted notification{Colors.END}")
        
        return result
    
    async def test_timezone_bypass_notification(self, token):
        """Test sending notification with timezone bypass."""
        title = "🌍 Timezone Bypass Test"
        body = "This notification bypasses timezone restrictions"
        
        if self.dry_run:
            print(f"{Colors.YELLOW}DRY RUN: Would send timezone bypass notification:{Colors.END}")
            print(f"  Title: {title}")
            print(f"  Body: {body}")
            return True
        
        self.logger.info("Testing timezone bypass notification...")
        
        # Send notification directly without timezone filtering
        result = send_notification(token, title, body)
        
        if result:
            print(f"{Colors.GREEN}✓ Timezone bypass notification sent!{Colors.END}")
            print(f"  This proves timezone restrictions can be bypassed")
            print(f"  Check your iOS app for: '{title}'")
        else:
            print(f"{Colors.RED}✗ Failed to send timezone bypass notification{Colors.END}")
        
        return result
    
    async def test_bulk_notification_single_user(self, token):
        """Test bulk notification function with single user."""
        title = "📦 Bulk Test (Single User)"
        body = "Testing bulk notification function with one recipient"
        
        if self.dry_run:
            print(f"{Colors.YELLOW}DRY RUN: Would send bulk notification:{Colors.END}")
            print(f"  Title: {title}")
            print(f"  Body: {body}")
            print(f"  Recipients: 1 (you)")
            return True
        
        self.logger.info("Testing bulk notification with single user...")
        
        try:
            await send_bulk_notification([token], title, body)
            print(f"{Colors.GREEN}✓ Bulk notification sent successfully!{Colors.END}")
            print(f"  Check your iOS app for: '{title}'")
            return True
        except Exception as e:
            print(f"{Colors.RED}✗ Failed to send bulk notification: {e}{Colors.END}")
            return False
    
    async def run_all_tests(self):
        """Run all real notification tests."""
        self.print_header()
        
        # Get user token
        token, user_timezone = self.get_user_token_and_timezone()
        
        if not token:
            print(f"{Colors.RED}❌ Cannot proceed: No FCM token found for user {self.target_user_id}{Colors.END}")
            print(f"{Colors.YELLOW}Make sure:{Colors.END}")
            print("  1. You're logged into the iOS app")
            print("  2. You've granted notification permissions")
            print("  3. The app has registered an FCM token")
            return False
        
        print(f"{Colors.GREEN}✓ Found FCM token for user{Colors.END}")
        print(f"  Token: {token[:20]}...")
        print(f"  Timezone: {user_timezone}")
        print()
        
        # Run tests
        tests = [
            ("Simple Notification", lambda: self.test_simple_notification(token)),
            ("Notification with Data", lambda: self.test_notification_with_data(token)),
            ("Formatted Notification", lambda: self.test_notification_message_format(token)),
            ("Timezone Bypass", None),  # Will be handled specially
            ("Bulk Notification", None)  # Will be handled specially
        ]
        
        results = []
        
        for i, (test_name, test_func) in enumerate(tests, 1):
            print(f"{Colors.BOLD}Test {i}: {test_name}{Colors.END}")
            print("-" * 50)

            try:
                if test_name == "Timezone Bypass":
                    # Handle async timezone bypass test
                    result = await self.test_timezone_bypass_notification(token)
                elif test_name == "Bulk Notification":
                    # Handle async bulk notification test
                    result = await self.test_bulk_notification_single_user(token)
                else:
                    # Handle sync tests
                    result = test_func()

                results.append((test_name, result))

                if result:
                    print(f"{Colors.GREEN}✓ {test_name} completed{Colors.END}")
                else:
                    print(f"{Colors.RED}✗ {test_name} failed{Colors.END}")

                # Wait between tests to avoid rate limiting
                if not self.dry_run and i < len(tests):
                    print(f"{Colors.YELLOW}Waiting 3 seconds before next test...{Colors.END}")
                    await asyncio.sleep(3)

            except Exception as e:
                print(f"{Colors.RED}✗ {test_name} failed with error: {e}{Colors.END}")
                results.append((test_name, False))

            print()
        
        # Print summary
        print("=" * 70)
        print(f"{Colors.BOLD}TEST SUMMARY{Colors.END}")
        print("=" * 70)
        
        passed = sum(1 for _, result in results if result)
        total = len(results)
        
        for test_name, result in results:
            status = "✓ PASS" if result else "✗ FAIL"
            color = Colors.GREEN if result else Colors.RED
            print(f"{color}{status}{Colors.END} {test_name}")
        
        print(f"\nResults: {passed}/{total} tests passed")
        
        if passed == total:
            print(f"\n{Colors.GREEN}{Colors.BOLD}🎉 ALL TESTS PASSED!{Colors.END}")
            print(f"{Colors.GREEN}You should have received {total} notifications on your iOS app.{Colors.END}")
        else:
            print(f"\n{Colors.YELLOW}⚠️  Some tests failed. Check the logs above.{Colors.END}")
        
        print(f"\n{Colors.BLUE}Next Steps:{Colors.END}")
        print("1. Check your iOS app for the test notifications")
        print("2. Verify notification permissions are enabled")
        print("3. Check notification delivery timing")
        print("4. Test with different notification types")
        
        return passed == total


async def main():
    parser = argparse.ArgumentParser(description='Test real push notifications to iOS app')
    parser.add_argument('--dry-run', action='store_true', 
                       help='Show what would be sent without actually sending notifications')
    
    args = parser.parse_args()
    
    if not args.dry_run:
        print(f"{Colors.RED}⚠️  WARNING: This will send REAL notifications to your iOS app!{Colors.END}")
        response = input("Continue? (yes/no): ")
        if response.lower() != 'yes':
            print("Test cancelled.")
            return
    
    tester = RealNotificationTester(dry_run=args.dry_run)
    success = await tester.run_all_tests()
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    asyncio.run(main())
