import asyncio
import concurrent.futures
import threading
from datetime import datetime
from datetime import time

import pytz

import database.chat as chat_db
import database.conversations as conversations_db
import database.notifications as notification_db
from models.notification_message import NotificationMessage
from utils.llm.external_integrations import get_conversation_summary
from utils.notifications import send_notification, send_bulk_notification
from utils.webhooks import day_summary_webhook


async def start_cron_job():
    print(f'start_cron_job called at {datetime.now(pytz.utc)}')
    if should_run_job():
        print('start_cron_job: should_run_job returned True, sending notifications...')
        try:
            await send_daily_notification()
            await send_daily_summary_notification()
            print('start_cron_job: All notifications sent successfully')
        except Exception as e:
            print(f'Error in start_cron_job: {e}')
            import traceback
            traceback.print_exc()
    else:
        print('start_cron_job: should_run_job returned False, no notifications to send')


def should_run_job():
    current_utc = datetime.now(pytz.utc)
    target_hours = {8, 22}
    print(f'should_run_job: Current UTC time: {current_utc}')

    matching_timezones = []
    for tz in pytz.all_timezones:
        try:
            local_time = current_utc.astimezone(pytz.timezone(tz))
            if local_time.hour in target_hours and local_time.minute == 0:
                matching_timezones.append(f"{tz} ({local_time.strftime('%H:%M')})")
        except Exception:
            # Skip invalid timezones
            continue

    if matching_timezones:
        print(f'should_run_job: Found {len(matching_timezones)} timezones at target hours: {matching_timezones[:5]}...')
        return True

    print('should_run_job: No timezones found at target hours (8 AM or 10 PM)')
    return False


async def send_daily_summary_notification():
    try:
        daily_summary_target_time = "22:00"
        timezones_in_time = _get_timezones_at_time(daily_summary_target_time)
        user_in_time_zone = await notification_db.get_users_id_in_timezones(timezones_in_time)
        if not user_in_time_zone:
            return None

        await _send_bulk_summary_notification(user_in_time_zone)
    except Exception as e:
        print(e)
        print("Error sending message:", e)
        return None


def _send_summary_notification(user_data: tuple):
    uid = user_data[0]
    fcm_token = user_data[1]
    daily_summary_title = "Here is your action plan for tomorrow"  # TODO: maybe include llm a custom message for this
    memories_data = conversations_db.filter_conversations_by_date(
        uid, datetime.combine(datetime.now().date(), time.min), datetime.now()
    )
    if not memories_data:
        return
    else:
        # Convert dictionary data to Conversation objects
        from models.conversation import Conversation
        memories = []
        for memory_dict in memories_data:
            try:
                memory = Conversation(**memory_dict)
                memories.append(memory)
            except Exception as e:
                print(f"Error converting memory to Conversation object: {e}")
                continue

        if not memories:
            return

        summary = get_conversation_summary(uid, memories)

    ai_message = NotificationMessage(
        text=summary,
        from_integration='false',
        type='day_summary',
        notification_type='daily_summary',
        navigate_to="/chat/omi",  # omi ~ no select
    )
    chat_db.add_summary_message(summary, uid)
    threading.Thread(target=day_summary_webhook, args=(uid, summary)).start()
    send_notification(fcm_token, daily_summary_title, summary, NotificationMessage.get_message_as_dict(ai_message))


async def _send_bulk_summary_notification(users: list):
    loop = asyncio.get_running_loop()
    with concurrent.futures.ThreadPoolExecutor() as pool:
        tasks = [loop.run_in_executor(pool, _send_summary_notification, uid) for uid in users]
        await asyncio.gather(*tasks)


async def send_daily_notification():
    try:
        print("Starting send_daily_notification...")
        morning_alert_title = "Memorion"
        morning_alert_body = "Wear your Memorion device to capture your conversations today."
        morning_target_time = "08:00"

        result = await _send_notification_for_time(morning_target_time, morning_alert_title, morning_alert_body)
        print(f"send_daily_notification completed. Users notified: {len(result) if result else 0}")
        return result

    except Exception as e:
        print(f"Error in send_daily_notification: {e}")
        import traceback
        traceback.print_exc()
        return None


async def _send_notification_for_time(target_time: str, title: str, body: str):
    user_in_time_zone = await _get_users_in_timezone(target_time)
    if not user_in_time_zone:
        print("No users found in time zone")
        return None
    await send_bulk_notification(user_in_time_zone, title, body)
    return user_in_time_zone


async def _get_users_in_timezone(target_time: str):
    timezones_in_time = _get_timezones_at_time(target_time)
    return await notification_db.get_users_token_in_timezones(timezones_in_time)


def _get_timezones_at_time(target_time):
    target_timezones = []
    for tz_name in pytz.all_timezones:
        tz = pytz.timezone(tz_name)
        current_time = datetime.now(tz).strftime("%H:%M")
        if current_time == target_time:
            target_timezones.append(tz_name)
    return target_timezones
