import json
import os
import sys
from datetime import datetime

import firebase_admin
import pytz

from modal import Image, App, Secret, Cron, Mount

# Define the app with proper mounts
app = App(
    name='omi-notifications',
    secrets=[Secret.from_name("gcp-credentials"), Secret.from_name('envs')],
)

# Create the image with necessary dependencies and mount the backend code
image = (
    Image.debian_slim()
    .apt_install('ffmpeg', 'git', 'unzip')
    .pip_install_from_requirements("requirements.txt")
    .env({"PYTHONPATH": "/root"})  # Set Python path to find modules
)


def should_run_job():
    """Check if we should run the job based on current time in different timezones"""
    current_utc = datetime.now(pytz.utc)
    target_hours = {8, 22}  # 8 AM and 10 PM

    for tz in pytz.all_timezones:
        local_time = current_utc.astimezone(pytz.timezone(tz))
        if local_time.hour in target_hours and local_time.minute == 0:
            return True

    return False


@app.function(
    image=image,
    schedule=Cron('* * * * *'),
    timeout=300,  # 5 minutes timeout
    memory=512,   # 512MB memory
    mounts=[Mount.from_local_dir(".", remote_path="/root")],  # Mount the entire backend directory
)
async def notifications_cronjob():
    """Notification cron job that sends daily notifications and summaries"""
    print(f'Notification cron job started at {datetime.now(pytz.utc)}')

    # Add current directory to Python path
    sys.path.insert(0, '/root')

    # Initialize Firebase
    try:
        if os.environ.get('SERVICE_ACCOUNT_JSON'):
            service_account_info = json.loads(os.environ["SERVICE_ACCOUNT_JSON"])
            credentials = firebase_admin.credentials.Certificate(service_account_info)
            firebase_admin.initialize_app(credentials)
            print('Firebase initialized with service account')
        else:
            firebase_admin.initialize_app()
            print('Firebase initialized with default credentials')
    except Exception as e:
        print(f'Firebase initialization error: {e}')
        return {"status": "error", "message": f"Firebase init failed: {e}"}

    # Check if we should run notifications
    if should_run_job():
        print('Notification job triggered - sending notifications...')
        try:
            # Import the full notification logic
            from utils.other.notifications import start_cron_job
            await start_cron_job()
            print('All notifications sent successfully')
            return {"status": "success", "message": "Notifications sent", "ran_at": str(datetime.now(pytz.utc))}
        except Exception as e:
            print(f'Error in notification job: {e}')
            import traceback
            traceback.print_exc()
            return {"status": "error", "message": str(e), "ran_at": str(datetime.now(pytz.utc))}
    else:
        print(f'Notification check at {datetime.now(pytz.utc)} - no notifications to send')
        return {"status": "skipped", "message": "No notifications needed", "ran_at": str(datetime.now(pytz.utc))}


@app.function(
    image=image,
    timeout=300,
    memory=512,
    mounts=[Mount.from_local_dir(".", remote_path="/root")],  # Mount the entire backend directory
)
async def manual_notification_trigger():
    """Manual trigger for testing notifications without timezone restrictions"""
    print(f'Manual notification trigger started at {datetime.now(pytz.utc)}')

    # Add current directory to Python path
    sys.path.insert(0, '/root')

    # Initialize Firebase
    try:
        if os.environ.get('SERVICE_ACCOUNT_JSON'):
            service_account_info = json.loads(os.environ["SERVICE_ACCOUNT_JSON"])
            credentials = firebase_admin.credentials.Certificate(service_account_info)
            firebase_admin.initialize_app(credentials)
            print('Firebase initialized with service account')
        else:
            firebase_admin.initialize_app()
            print('Firebase initialized with default credentials')
    except Exception as e:
        print(f'Firebase initialization error: {e}')
        return {"status": "error", "message": f"Firebase init failed: {e}"}

    try:
        # Import and run the full notification logic
        from utils.other.notifications import start_cron_job
        await start_cron_job()
        print('Manual notification trigger completed successfully')
        return {"status": "success", "message": "Manual notifications sent", "ran_at": str(datetime.now(pytz.utc))}
    except Exception as e:
        print(f'Error in manual notification trigger: {e}')
        import traceback
        traceback.print_exc()
        return {"status": "error", "message": str(e), "ran_at": str(datetime.now(pytz.utc))}