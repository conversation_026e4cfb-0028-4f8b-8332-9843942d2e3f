import json
import os
import sys
import firebase_admin
from modal import Image, App, Secret, Mount

app = App(
    name='test-firebase',
    secrets=[Secret.from_name("gcp-credentials"), Secret.from_name('envs')],
)

image = (
    Image.debian_slim()
    .pip_install("firebase-admin")
    .env({"PYTHONPATH": "/root"})
)

@app.function(
    image=image,
    mounts=[Mount.from_local_dir(".", remote_path="/root")]
)
def test_firebase_connection():
    """Test Firebase connection and basic functionality"""
    # Add current directory to Python path
    sys.path.insert(0, '/root')

    try:
        # Initialize Firebase
        if os.environ.get('SERVICE_ACCOUNT_JSON'):
            service_account_info = json.loads(os.environ["SERVICE_ACCOUNT_JSON"])
            credentials = firebase_admin.credentials.Certificate(service_account_info)
            firebase_admin.initialize_app(credentials)
            print('✓ Firebase initialized successfully with service account')
        else:
            firebase_admin.initialize_app()
            print('✓ Firebase initialized with default credentials')
        
        # Test Firestore connection
        from firebase_admin import firestore
        db = firestore.client()
        
        # Try to read from users collection (just count)
        users_ref = db.collection('users')
        users_count = len(list(users_ref.limit(1).stream()))
        print(f'✓ Firestore connection successful. Can access users collection.')
        
        # Test Firebase Messaging
        from firebase_admin import messaging
        print('✓ Firebase Messaging module imported successfully')
        
        return {
            "status": "success", 
            "message": "Firebase connection test passed",
            "firestore_accessible": True,
            "messaging_available": True
        }
        
    except Exception as e:
        print(f'✗ Firebase connection test failed: {e}')
        import traceback
        traceback.print_exc()
        return {
            "status": "error", 
            "message": str(e),
            "firestore_accessible": False,
            "messaging_available": False
        }
